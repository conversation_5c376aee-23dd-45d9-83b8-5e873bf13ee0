using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.NhanhDtos;
using App.ECommerce.Resource.Dtos.Webhooks;
using App.ECommerce.Units.Abstractions.Entities;

namespace App.ECommerce.Helpers.Interface;

public interface INhanhHelper
{
    Task<Result<SyncServiceConfig>> SaveNhanhConfig(SyncServiceConfigDto dto);
    Task<SyncServiceConfig> GetNhanhConfig(string shopId);
    Task<Result<bool>> DeleteNhanhConfig(string shopId);
    Task<Result<SyncServiceConfig>> UpdateNhanhAccessCode(string shopId, string accessCode);
    Task<Result<bool>> SyncNhanhProductFromWebhook(object productData, string shopId);
    Task<Result<bool>> SyncNhanhOrderFromWebhook(object orderData, string shopId);
    Task<Result<bool>> SyncNhanhCustomerFromWebhook(object customerData, string shopId);
    Task<Result<bool>> DeleteNhanhProductsFromWebhook(object productIds, string shopId);
    Task<Result<bool>> DeleteNhanhOrderFromWebhook(object orderData, string shopId);
    Task<Result<bool>> CreateOrderToNhanh(Order order, string shopId);
    Task<Result<bool>> UpdateOrderToNhanh(Order order, string shopId);
    Task<Result<List<NhanhProductCategoryDto>>> GetNhanhCategoriesAsync(string shopId);
}