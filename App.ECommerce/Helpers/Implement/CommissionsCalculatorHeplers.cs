﻿using App.ECommerce.Helpers.Interface;
using App.ECommerce.Repository.Entities;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.Helpers.Implement
{
    public class CommissionsCalculatorHeplers : ICommissionsCalculatorHeplers
    {
        public decimal CalculateTotalCommissionValue(List<Order> orders, CommissionLevel commissionLevel)
        {

            // Tính tổng Value từ CommissionDistribution
            decimal totalValue = orders
                .Where(order => order?.CommissionBreakUp?.CommissionDistribution != null) // Lọc các order có CommissionDistribution hợp lệ
                .SelectMany(order => order.CommissionBreakUp.CommissionDistribution) // L<PERSON>y tất cả CommissionDistribution
                .Where(distribution => distribution.CommissionLevel == commissionLevel && distribution.IsActive) // Lọc theo CommissionLevel
                .Sum(distribution => (decimal)distribution.Value); // Tính tổng Value, ép kiểu về decimal

            return totalValue;
        }

        public (decimal revenue, decimal commission) CalculateCommissionAndRevenue(List<Order> orders, string userId)
        {
            decimal totalRevenue = 0m;
            decimal totalCommission = 0m;

            foreach (var order in orders)
            {
                if (order.CommissionBreakUp == null || order.CommissionBreakUp.CommissionDistribution == null)
                {
                    continue;
                }

                // Tìm CommissionDistribution có UserId khớp và IsActive = true
                var matchingDistribution = order.CommissionBreakUp.CommissionDistribution
                .FirstOrDefault(dist => dist.UserId == userId && dist.IsActive);

                if (matchingDistribution == null)
                {
                    continue;
                }

                // Tính commission từ Value (ép kiểu sang decimal)
                totalCommission += Math.Round(Convert.ToDecimal(matchingDistribution.Value));

                // Tính revenue: Price - TransportPrice - VoucherPromotionPrice (ép kiểu sang decimal)
                var revenue = (order.Price) - (order.TransportPrice) - (order.VoucherPromotionPrice);

                totalRevenue += Convert.ToDecimal(revenue);
            }

            return (totalRevenue, totalCommission);
        }
    }
}
