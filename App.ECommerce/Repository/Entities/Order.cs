using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Units;
using App.ECommerce.Units.Enums;

using CommunityToolkit.HighPerformance.Helpers;

using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Repository.Entities;

public enum TypeDelivery
{
    InShop,
    ExpressDelivery,
}

public enum TypePayment
{
    COD,
    Vnpay,
    Momo,
    Zalo,
    Transfer,
    Cash,
    Sepay,
    Other,
}

public enum TypeOrderStatus
{
    Pending,
    Verified,
    Paid,
    Success,
    Failed,
    Refund,
}

public enum TypeTransportStatus
{
    Created,
    Verified,
    WaitingForDelivery,
    Delivered,
    Transporting,
    Delivering,
    Success,
    Waiting,
    Refunding,
    Refunded,
    Cancel,
}

public enum TypePayStatus
{
    NotPaid,
    Paid,
    Refund,
}

public enum TypeTransportService
{
    LCOD,
    NCOD,
    VHT,
    PHS,
    PTN,
    VCN,
    VTK,
    AHAMOVE,
    JTEXPRESS,
    EMS,        // EMS Vietnam
    NINJAVAN,   // <PERSON>van
    BESTEXPRESS // Best Express
}

public enum TypeServe
{
    WaitingForServe,
    Served,
    Success,
}

public enum TypeOrigin
{
    WebPartner,
    ZaloMiniApp,
    WebApp,
    Pos,
    Website,
    Affiliation,
    Other,
    AFFILIATION,
    Nhanhvn,
    SapoOmni,

}

[BsonIgnoreExtraElements]
public class Order
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string Id { get; set; }

    [Display(Name = "OrderId")]
    [BsonElement("OrderId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string OrderId { get; set; }

    [Display(Name = "OrderNo")]
    [BsonElement("OrderNo")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? OrderNo { get; set; }

    [Display(Name = "TransportId")]
    [BsonElement("TransportId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? TransportId { get; set; }

    [Display(Name = "PaymentId")]
    [BsonElement("PaymentId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? PaymentId { get; set; }

    [Display(Name = "TransactionId")]
    [BsonElement("TransactionId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string TransactionId { get; set; }

    [Display(Name = "PartnerId")]
    [BsonElement("PartnerId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string PartnerId { get; set; }

    [Display(Name = "UserShippingAddress")]
    [BsonElement("UserShippingAddress")]
    public ShippingAddress UserShippingAddress { get; set; }

    [Display(Name = "Creator")]
    [BsonElement("Creator")]
    public ShippingAddress Creator { get; set; }

    [Display(Name = "ShopId")]
    [BsonElement("ShopId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string ShopId { get; set; }

    [Display(Name = "ShopName")]
    [BsonElement("ShopName")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopName { get; set; }

    [Display(Name = "ShopProvinceId")]
    [BsonElement("ShopProvinceId")]
    [BsonRepresentation(BsonType.String)]
    public string? ShopProvinceId { get; set; }

    [Display(Name = "ShopProvinceName")]
    [BsonElement("ShopProvinceName")]
    [BsonRepresentation(BsonType.String)]
    public string? ShopProvinceName { get; set; }

    [Display(Name = "ShopDistrictId")]
    [BsonElement("ShopDistrictId")]
    [BsonRepresentation(BsonType.String)]
    public string? ShopDistrictId { get; set; }

    [Display(Name = "ShopDistrictName")]
    [BsonElement("ShopDistrictName")]
    [BsonRepresentation(BsonType.String)]
    public string? ShopDistrictName { get; set; }

    [Display(Name = "ShopWardId")]
    [BsonElement("ShopWardId")]
    [BsonRepresentation(BsonType.String)]
    public string? ShopWardId { get; set; }

    [Display(Name = "ShopWardName")]
    [BsonElement("ShopWardName")]
    [BsonRepresentation(BsonType.String)]
    public string? ShopWardName { get; set; }

    [Display(Name = "ShopAddress")]
    [BsonElement("ShopAddress")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopAddress { get; set; }

    [Display(Name = "Notes")]
    [BsonElement("Notes")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? Notes { get; set; }

    [Display(Name = "OrderOrigin")]
    [BsonElement("OrderOrigin")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))] // Newtonsoft.Json
    public TypeOrigin? OrderOrigin { get; set; }

    [Required]
    [Display(Name = "ListItems")]
    [BsonElement("ListItems")]
    [DefaultValue("[]")]
    public List<ItemsOrder> ListItems { get; set; }

    [Display(Name = "VoucherPromotionIds")]
    [BsonElement("VoucherPromotionIds")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("[]")]
    public List<string>? VoucherPromotionIds { get; set; } = new List<string>();

    [Display(Name = "VoucherPromotion")]
    [BsonElement("VoucherPromotion")]
    [DefaultValue("[]")]
    public List<Voucher>? VoucherPromotion { get; set; } = new List<Voucher>();

    [Display(Name = "VoucherTransportIds")]
    [BsonElement("VoucherTransportIds")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("[]")]
    public List<string>? VoucherTransportIds { get; set; } = new List<string>();

    [Display(Name = "VoucherTransport")]
    [BsonElement("VoucherTransport")]
    [DefaultValue("[]")]
    public List<Voucher>? VoucherTransport { get; set; } = new List<Voucher>();

    [Display(Name = "Price")]
    [BsonElement("Price")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public long Price { get; set; } = 0;

    [Display(Name = "ExchangePoints")]
    [BsonElement("ExchangePoints")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0.0)]
    public int? ExchangePoints { get; set; } = 0;

    [Display(Name = "PointAfterCompleteOrder")]
    [BsonElement("PointAfterCompleteOrder")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public int? PointAfterCompleteOrder { get; set; } = 0;

    [Display(Name = "PointPrice")]
    [BsonElement("PointPrice")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public long PointPrice { get; set; } = 0;

    [Display(Name = "VoucherPromotionPrice")]
    [BsonElement("VoucherPromotionPrice")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public long VoucherPromotionPrice { get; set; } = 0;

    [Display(Name = "VoucherTransportPrice")]
    [BsonElement("VoucherTransportPrice")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public long VoucherTransportPrice { get; set; } = 0;

    [Display(Name = "TransportPrice")]
    [BsonElement("TransportPrice")]
    [BsonRepresentation(BsonType.Int64)]
    [DefaultValue(0)]
    public long TransportPrice { get; set; } = 0;

    [Display(Name = "TransportService")]
    [BsonElement("TransportService")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(null)]
    public TypeTransportService? TransportService { get; set; } = null;

    [Display(Name = "StatusTransport")]
    [BsonElement("StatusTransport")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(TypeTransportStatus.Created)]
    public TypeTransportStatus? StatusTransport { get; set; } = TypeTransportStatus.Created;

    [Display(Name = "StatusDelivery")]
    [BsonElement("StatusDelivery")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(TypeDelivery.InShop)]
    public TypeDelivery StatusDelivery { get; set; } = TypeDelivery.InShop;

    [Display(Name = "StatusOrder")]
    [BsonElement("StatusOrder")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(TypeOrderStatus.Pending)]
    public TypeOrderStatus StatusOrder { get; set; } = TypeOrderStatus.Pending;

    [Display(Name = "TypePay")]
    [BsonElement("TypePay")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(TypePayment.COD)]
    public TypePayment TypePay { get; set; } = TypePayment.COD;

    [Display(Name = "StatusPay")]
    [BsonElement("StatusPay")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(TypePayStatus.NotPaid)]
    public TypePayStatus? StatusPay { get; set; } = TypePayStatus.NotPaid;

    [Display(Name = "StatusServe")]
    [BsonElement("StatusServe")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    [DefaultValue(null)]
    public TypeServe? StatusServe { get; set; } = null;

    [Display(Name = "Status")]
    [BsonElement("Status")]
    [BsonRepresentation(BsonType.String)] // Mongo
    [JsonConverter(typeof(StringEnumConverter))] // Newtonsoft.Json
    [DefaultValue(TypeStatus.Actived)]
    public TypeStatus Status { get; set; } = TypeStatus.Actived;

    [Display(Name = "BranchId")]
    [BsonElement("BranchId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string BranchId { get; set; }

    [Display(Name = "BranchDetail")]
    [BsonElement("BranchDetail")]
    public Branch BranchDetail { get; set; }

    [Display(Name = "TransportOrderId")]
    [BsonElement("TransportOrderId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string TransportOrderId { get; set; }

    [Display(Name = "Created")]
    [BsonElement("Created")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime Created { get; set; } = DateTimes.Now();

    [Display(Name = "Updated")]
    [BsonElement("Updated")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime Updated { get; set; } = DateTimes.Now();

    [Display(Name = "PaymentInfo")]
    [BsonElement("PaymentInfo")]
    public Payment PaymentInfo { get; set; }

    [Display(Name = "CommissionBreakUp")]
    [BsonElement("CommissionBreakUp")]
    public CommissionBreakUp? CommissionBreakUp { get; set; }

    [Display(Name = "CompletedAt")]
    [BsonElement("CompletedAt")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime? CompletedAt { get; set; }

    [Display(Name = "TransportOrderLabel")]
    [BsonElement("TransportOrderLabel")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? TransportOrderLabel { get; set; }

    [BsonElement("TaxInvoice")]
    public TaxInvoiceConfiguration? TaxInvoice { get; set; }

    [BsonElement("TaxSummary")]
    public List<TaxSummaryLine>? TaxSummary { get; set; }

    [BsonElement("TotalTaxAmount")]
    [BsonRepresentation(BsonType.Decimal128)]
    [DefaultValue(0)]
    public decimal TotalTaxAmount { get; set; }

    [BsonElement("TotalAfterTax")]
    [BsonRepresentation(BsonType.Decimal128)]
    [DefaultValue(0)]
    public decimal TotalAfterTax { get; set; }

    [Display(Name = "VoucherCodes")]
    [BsonElement("VoucherCodes")]
    public List<string> VoucherCodes { get; set; } = new List<string>();


    [Display(Name = "ExternalSource")]
    [BsonElement("ExternalSource")]
    [BsonRepresentation(BsonType.String)]
    public SyncServiceEnum? ExternalSource { get; set; }

    [Display(Name = "ExternalId")]
    [BsonElement("ExternalId")]
    [BsonRepresentation(BsonType.String)]
    public string? ExternalId { get; set; }

    [Display(Name = "TrackingUrl")]
    [BsonElement("TrackingUrl")]
    [BsonRepresentation(BsonType.String)]
    public string? TrackingUrl { get; set; }

}