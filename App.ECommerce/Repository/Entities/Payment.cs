using App.Base.Utilities;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
namespace App.ECommerce.Repository.Entities;

public enum PaymentPlatformType
{
    ZaloMiniApp,
    Pos,
    WebApp
}

[BsonIgnoreExtraElements]
public class Payment
{
    [BsonId]
    [BsonRepresentation(BsonType.ObjectId)]
    public string? Id { get; set; }

    [Display(Name = "PaymentId")]
    [BsonElement("PaymentId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? PaymentId { get; set; }

    [Display(Name = "Name")]
    [BsonElement("Name")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? Name { get; set; }

    [Display(Name = "Position")]
    [BsonElement("Position")]
    [BsonRepresentation(BsonType.Int32)]
    public int? Position { get; set; }

    [Display(Name = "IsActive")]
    [BsonElement("IsActive")]
    [BsonRepresentation(BsonType.Boolean)]
    public Boolean IsActive { get; set; }

    [Display(Name = "Detail")]
    [BsonElement("Detail")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? Detail { get; set; }

    [Display(Name = "ShopId")]
    [BsonElement("ShopId")]
    [BsonRepresentation(BsonType.String)]
    [DefaultValue("")]
    public string? ShopId { get; set; }

    [Display(Name = "Platform")]
    [BsonElement("Platform")]
    public List<string> Platform { get; set; } = new List<string> { };

    [Display(Name = "PaymentPhoto")]
    [BsonElement("PaymentPhoto")]
    [BsonRepresentation(BsonType.String)]
    public string? PaymentPhoto { get; set; }

    [Display(Name = "TypePay")]
    [BsonElement("TypePay")]
    [BsonRepresentation(BsonType.String)]
    [JsonConverter(typeof(StringEnumConverter))]
    public TypePayment? TypePay { get; set; }

    [Display(Name = "BankShortCode")]
    [BsonElement("BankShortCode")]
    [BsonRepresentation(BsonType.String)]
    public string? BankShortCode { get; set; }

    [Display(Name = "BankName")]
    [BsonElement("BankName")]
    [BsonRepresentation(BsonType.String)]
    public string? BankName { get; set; }

    [Display(Name = "BankNumber")]
    [BsonElement("BankNumber")]
    [BsonRepresentation(BsonType.String)]
    public string? BankNumber { get; set; }

    [Display(Name = "BankUserName")]
    [BsonElement("BankUserName")]
    [BsonRepresentation(BsonType.String)]
    public string? BankUserName { get; set; }

    [Display(Name = "BankAccountNumber")]
    [BsonElement("BankAccountNumber")]
    [BsonRepresentation(BsonType.String)]
    public string? BankAccountNumber { get; set; }

    [Display(Name = "BankAccountId")]
    [BsonElement("BankAccountId")]
    [BsonRepresentation(BsonType.String)]
    public string? BankAccountId { get; set; }

    [Display(Name = "CustomerBankName")]
    [BsonElement("CustomerBankName")]
    [BsonRepresentation(BsonType.String)]
    public string? CustomerBankName { get; set; }

    [Display(Name = "IdentificationNumber")]
    [BsonElement("IdentificationNumber")]
    [BsonRepresentation(BsonType.String)]
    public string? IdentificationNumber { get; set; }

    [Display(Name = "PhoneNumber")]
    [BsonElement("PhoneNumber")]
    [BsonRepresentation(BsonType.String)]
    public string? PhoneNumber { get; set; }

    [Display(Name = "Alias")]
    [BsonElement("Alias")]
    [BsonRepresentation(BsonType.String)]
    public string? Alias { get; set; }

    [Display(Name = "Created")]
    [BsonElement("Created")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime? Created { get; set; } = DateTimesEx.Now();

    [Display(Name = "Updated")]
    [BsonElement("Updated")]
    [BsonDateTimeOptions(Kind = DateTimeKind.Unspecified)]
    [BsonRepresentation(BsonType.String)]
    public DateTime? Updated { get; set; } = DateTimesEx.Now();
}