﻿using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;

using MongoDB.Driver;

namespace App.ECommerce.Repository.Interface;

public interface ICommissionsReportRepository
{
    public Task<bool> InsertCommissionReports(List<CommissionsReport> inputDtos);
    public Task<PagingResult<CommissionsReport>> GetCommissionsReport(GetPartnerCommissionsByMonthYearInputDto inputDto);
    public Task<List<CommissionsReport>> GetCommissionsReportByFilter(FilterDefinition<CommissionsReport> filter);
    PagingResult<CommissionsReport> ListUserCommissionReport(Paging paging, string? userId = null);
    Task<long> TotalCommissionValue(string userId);
}