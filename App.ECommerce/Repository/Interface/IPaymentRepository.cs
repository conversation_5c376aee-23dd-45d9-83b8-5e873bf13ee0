using App.ECommerce.Repository.Entities;
using App.ECommerce.Resource.Model;

namespace App.ECommerce.Repository.Interface;

public interface IPaymentRepository
{
    //=== Payment
    Payment CreateItems(Payment item);
    Payment DeleteItems(string itemsId);
    Payment? FindByItemsId(string itemsId);
    Payment? FindByPlatform(string shopId, string platform);
    PagingResult<Payment> ListItems(Paging paging, string? shopId = null);
    PagingResult<Payment> ListItemsUser(Paging paging, string shopId, PaymentPlatformType paymentPlatformType);
    Payment? UpdateItems(Payment item);
    Payment? FindByShopIdAndTypePay(string shopId, string platform, TypePayment typePay);
}