using System;
using App.ECommerce.ProcessFlow.Models;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Units.Enums;
using App.ECommerce.Units.Enums.Order;
using Microsoft.Extensions.Localization;

namespace App.ECommerce.ProcessFlow.Interface;

public interface IOrderFlow
{
    Task<Result<OrderDto>> CreateOrder(OrderFlowModel obj);
    Task<(bool Success, string Message)> UpdateOrderStatus(Order order, TypeTransportStatus transportStatus, TypeOrderStatus? orderStatus = null);
    Task<(bool Success, string Message)> UpdateOrderNotes(Order order, string notes);
    Task<(bool Success, string Message)> CancelOrder(Order order);
    Task<(bool Success, string Message)> UpdateOrderPayment(Order order);
    (bool Success, string Message) ValidateOrderUpdate(Order order, UpdateOrderInfoActionEnum updateAction);
    Task DeleteAllOrder(string shopId);
    Task DeleteOrder(string orderId);
    Task OrdersProcessing(List<Order> listOrder, TypeOrderStatus typeStatusOrder);
    Task<Result<bool>> SyncOrderToExternalServicesAsync(Order order);
}
