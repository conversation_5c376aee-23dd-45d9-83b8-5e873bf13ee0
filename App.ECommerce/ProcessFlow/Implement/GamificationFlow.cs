using App.ECommerce.Helpers;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using log4net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Security.Cryptography;
using App.ECommerce.Repository.Entities;
using MongoDB.Bson;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Localization;
using App.ECommerce.Resource.Dtos.GamificationDtos;
using App.ECommerce.Units;
using App.ECommerce.Units.Abstractions.Entities;

namespace App.ECommerce.ProcessFlow.Implement;

public class GamificationFlow : IGamificationFlow
{
    private readonly string _promogameApiUrl;
    private readonly string _dashboardUrl;
    private readonly string _domainWebhook;
    private readonly IGamificationRepository _gamificationRepository;
    private readonly IGamificationShopRepository _gamificationShopRepository;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IShopRepository _shopRepository;
    private readonly ILog _log = LogManager.GetLogger(typeof(GamificationFlow));
    private readonly IStringLocalizer _localizer;
    private readonly IShopSettingRepository _shopSettingRepository;
    private readonly IPrizeRepository _prizeRepository;

    public GamificationFlow(
        IGamificationRepository gamificationRepository,
        IHttpClientFactory httpClientFactory,
        IShopRepository shopRepository,
        IGamificationShopRepository gamificationShopRepository,
        IShopSettingRepository shopSettingRepository,
        IStringLocalizer localizer,
        IConfiguration configuration,
        IPrizeRepository prizeRepository)
    {
        _gamificationRepository = gamificationRepository;
        _httpClientFactory = httpClientFactory;
        _shopRepository = shopRepository;
        _gamificationShopRepository = gamificationShopRepository;
        _shopSettingRepository = shopSettingRepository;
        _localizer = localizer;
        _promogameApiUrl = configuration["Promogame:ApiUrl"];
        _dashboardUrl = configuration["Promogame:DashboardUrl"];
        _domainWebhook = configuration["Promogame:DomainWebhook"];
        _prizeRepository = prizeRepository;
    }

    private async Task<Result<TResponseData>> ExecuteGraphQLRequestAsync<TVariables, TResponseData>(
        string queryOrMutation,
        TVariables variables,
        string accessToken = null,
        bool isRetry = false)
    {
        var requestBody = new PromogameGraphQLRequest<TVariables>
        {
            Query = queryOrMutation,
            Variables = variables
        };

        using var client = _httpClientFactory.CreateClient("PromogameClient");
        client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");

        if (!string.IsNullOrEmpty(accessToken))
        {
            client.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        }

        var serializerOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
        };
        var requestJson = JsonSerializer.Serialize(requestBody, serializerOptions);
        var content = new StringContent(requestJson, Encoding.UTF8, "application/json");

        _log.Debug($"Promogame API Request to {_promogameApiUrl}. Body: {requestJson}");
        HttpResponseMessage responseMessage;
        try
        {
            responseMessage = await client.PostAsync(_promogameApiUrl, content);
        }
        catch (HttpRequestException ex)
        {
            _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
            return Result<TResponseData>.Failure("Không thể kết nối đến dịch vụ gamification");
        }

        var responseContent = await responseMessage.Content.ReadAsStringAsync();
        _log.Debug($"Promogame API Response: {responseContent}");

        if (!responseMessage.IsSuccessStatusCode)
        {
            _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
            return Result<TResponseData>.Failure("Yêu cầu đến dịch vụ gamification thất bại");
        }

        var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<TResponseData>>(responseContent,
            new JsonSerializerOptions { PropertyNameCaseInsensitive = true, Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) } });

        if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
        {
            var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
            _log.Error($"GraphQL error: {errorMessages}. Query: {queryOrMutation}");

            // Check if token is expired and this is not a retry attempt
            if (!isRetry && IsTokenExpiredError(errorMessages))
            {
                _log.Info("Token expired, attempting to refresh and retry...");
                var refreshResult = await RefreshTokenAndRetryAsync();
                if (refreshResult.IsSuccess)
                {
                    return await ExecuteGraphQLRequestAsync<TVariables, TResponseData>(queryOrMutation, variables, refreshResult.Data, true);
                }
                return Result<TResponseData>.Failure("Phiên đăng nhập đã hết hạn");
            }

            return Result<TResponseData>.Failure("Lỗi xử lý dữ liệu từ dịch vụ gamification");
        }
        return Result<TResponseData>.Success(gqlResponse.Data);
    }

    private bool IsTokenExpiredError(string errorMessage)
    {
        var expiredKeywords = new[] { "token", "expired", "unauthorized", "authentication", "invalid token" };
        return expiredKeywords.Any(keyword => errorMessage.ToLowerInvariant().Contains(keyword));
    }

    private async Task<Result<string>> RefreshTokenAndRetryAsync()
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found for token refresh");
                return Result<string>.Failure("Không tìm thấy cấu hình gamification");
            }

            _log.Info($"Refreshing token for user: {settings.Username}");
            var loginData = await LoginAsync(settings.Username, settings.Password);

            if (loginData == null || string.IsNullOrEmpty(loginData.AccessToken))
            {
                _log.Error("Failed to refresh token - login returned null or empty token");
                return Result<string>.Failure("Không thể làm mới token đăng nhập");
            }

            // Update settings with new tokens
            settings.AccessToken = loginData.AccessToken;
            settings.RefreshToken = loginData.RefreshToken;
            await _gamificationRepository.UpdateSettingsAsync(settings);

            _log.Info("Token refreshed successfully");
            return Result<string>.Success(loginData.AccessToken);
        }
        catch (Exception ex)
        {
            _log.Error("Failed to refresh token", ex);
            return Result<string>.Failure("Lỗi khi làm mới token đăng nhập");
        }
    }

    private async Task<Result<TResponseData>> ExecuteGraphQLRequestWithTokenRefreshAsync<TVariables, TResponseData>(
        Gamification settings,
        string queryOrMutation,
        TVariables variables,
        Func<string, Task<Result<TResponseData>>> executeWithToken)
    {
        var result = await executeWithToken(settings.AccessToken);
        if (result.IsSuccess)
        {
            return result;
        }

        // Check if this is a token expiration error
        if (result.Message.Contains("Phiên đăng nhập đã hết hạn"))
        {
            _log.Info("Token expired, attempting to refresh and retry...");
            var refreshResult = await RefreshTokenAndRetryAsync();
            if (refreshResult.IsSuccess)
            {
                return await executeWithToken(refreshResult.Data);
            }
            return Result<TResponseData>.Failure("Phiên đăng nhập đã hết hạn");
        }

        return result;
    }

    private async Task<PromogameTokenDto> LoginAsync(string username, string password)
    {
        var mutation = @"
                mutation ($username: String!, $password: String!){
                    login( input: { username: $username, password: $password }) {
                        accessToken
                        refreshToken
                    }
                }
            ";
        var variables = new PromogameLoginRequestDto
        {
            Username = username,
            Password = password
        };
        var loginResult = await ExecuteGraphQLRequestAsync<PromogameLoginRequestDto, PromogameLoginResponseDto>(mutation, variables);
        if (loginResult.IsSuccess)
        {
            return loginResult.Data?.Login;
        }
        return null;
    }

    private async Task<Gamification> EnsureValidGamificationTokenAsync(string shopId)
    {
        var settings = await _gamificationRepository.GetSettingsAsync();

        var loginData = await LoginAsync(settings.Username, settings.Password);
        if (loginData == null || string.IsNullOrEmpty(loginData.AccessToken) || string.IsNullOrEmpty(loginData.RefreshToken))
        {
            _log.Error($"Incomplete login data received from gamification service for shopId {shopId}.");
            return null;
        }
        settings.AccessToken = loginData.AccessToken;
        settings.RefreshToken = loginData.RefreshToken;
        await _gamificationRepository.UpdateSettingsAsync(settings);
        return settings;
    }

    public async Task<Result<PrizeResponseDto>> CreatePrizeAsync(CreatePrizeInputDto input)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return Result<PrizeResponseDto>.Failure("Không tìm thấy cấu hình gamification");
            }

            // 1. Tạo Prize ở DB mình trước
            var prize = new Prize
            {
                Id = Guid.NewGuid(),
                PrizeGameId = Guid.NewGuid().ToString(),
                Name = input.Name,
                Type = input.Type,
                AvatarLink = null,
                Quantity = input.Quantity,
                Stock = input.Stock,
                ExternalPrizeId = input.ExternalPrizeId,
                Category = input.Category
            };
            await _prizeRepository.InsertAsync(prize);

            // 3. Gọi API bên thứ 3, truyền externalPrizeId = myPrize.PrizeId
            var mutation = @"
                mutation CreatePrize(
                    $name: String!,
                    $avatar: Upload!,
                    $quantity: Int!,
                    $campaignId: String!,
                    $type: PrizeType!,
                    $stock: Int,
                    $externalPrizeId: String
                ){
                  createPrize(
                    input: {
                      name: $name
                      avatar: { file: $avatar }
                      quantity: $quantity
                      campaignId: $campaignId
                      type: $type
                      stock: $stock
                      externalPrizeId: $externalPrizeId
                    }
                  ) {
                    id
                    name
                    type
                    avatarLink
                    quantity
                    stock
                    externalPrizeId
                  }
                }
            ";
            var operationsVariables = new
            {
                name = input.Name,
                avatar = (object)null,
                quantity = input.Quantity,
                campaignId = input.CampaignId,
                type = input.Type.ToString().ToUpperInvariant(),
                stock = input.Stock,
                externalPrizeId = prize.PrizeGameId
            };
            var operationsObject = new
            {
                Query = mutation,
                Variables = operationsVariables
            };
            var requestSerializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
            var mapJson = "{\"0\": [\"variables.avatar\"]}";
            using var client = _httpClientFactory.CreateClient("PromogameClient");
            client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");
            if (!string.IsNullOrEmpty(settings.AccessToken))
            {
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", settings.AccessToken);
            }
            client.DefaultRequestHeaders.Add("X-Apollo-Operation-Name", "CreatePrize");
            using var multipartContent = new MultipartFormDataContent();
            multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
            multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");
            if (input.Avatar != null && input.Avatar.Length > 0)
            {
                multipartContent.Add(new StreamContent(input.Avatar.OpenReadStream()), "0", input.Avatar.FileName);
            }
            _log.Debug($"Promogame API Request (Multipart) to {_promogameApiUrl}. Operations: {operationsJson}, Map: {mapJson}");
            HttpResponseMessage responseMessage;
            try
            {
                responseMessage = await client.PostAsync(_promogameApiUrl, multipartContent);
            }
            catch (HttpRequestException ex)
            {
                _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
                return Result<PrizeResponseDto>.Failure("Không thể kết nối đến dịch vụ gamification");
            }
            var responseContent = await responseMessage.Content.ReadAsStringAsync();
            _log.Debug($"Promogame API Response: {responseContent}");
            if (!responseMessage.IsSuccessStatusCode)
            {
                _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
                return Result<PrizeResponseDto>.Failure("Yêu cầu đến dịch vụ gamification thất bại");
            }
            var responseDeserializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<CreatePrizeResponseDto>>(responseContent, responseDeserializerOptions);
            if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
            {
                var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
                _log.Error($"GraphQL error: {errorMessages}. Query: {mutation}");
                return Result<PrizeResponseDto>.Failure("Lỗi từ dịch vụ gamification");
            }
            // 4. Sau khi thành công, cập nhật lại các trường còn lại vào Prize bên mình
            if (gqlResponse?.Data?.CreatePrize != null)
            {
                prize.AvatarLink = gqlResponse.Data.CreatePrize.AvatarLink;
                prize.PrizeId = gqlResponse.Data.CreatePrize.Id;
                await _prizeRepository.UpdateAsync(prize);
            }
            return Result<PrizeResponseDto>.Success(gqlResponse.Data.CreatePrize);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi tạo prize: {ex.Message}", ex);
            return Result<PrizeResponseDto>.Failure("Không thể tạo phần thưởng");
        }
    }

    public async Task<Result<PrizeResponseDto>> UpdatePrizeAsync(UpdatePrizeInputDto input)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found for ShopId: " + input.ShopId);
                return Result<PrizeResponseDto>.Failure("Không tìm thấy cấu hình gamification");
            }
            var mutation = @"
                mutation UpdatePrize($id: String!, $name: String!, $quantity: Int!, $stock: Int){
                    updatePrize(input: { id: $id, name: $name, quantity: $quantity, stock: $stock}) {
                        id
                        name
                        type
                        avatarLink
                        quantity
                        stock
                        externalPrizeId
                    }
                }
            ";
            var variables = new
            {
                id = input.Id,
                name = input.Name,
                quantity = input.Quantity,
                stock = input.Stock
            };
            var responseResult = await ExecuteGraphQLRequestAsync<object, UpdatePrizeResponseWrapperDto>(mutation, variables, settings.AccessToken);
            if (!responseResult.IsSuccess)
            {
                return Result<PrizeResponseDto>.Failure("Lỗi khi cập nhật phần thưởng");
            }

            var updatedPrize = responseResult.Data.UpdatePrize;
            if (updatedPrize != null)
            {
                var prize = await _prizeRepository.GetByPrizeIdAsync(updatedPrize.Id);
                prize.Name = updatedPrize.Name;
                prize.Quantity = updatedPrize.Quantity;
                await _prizeRepository.UpdateByPrizeIdAsync(prize);
            }
            return Result<PrizeResponseDto>.Success(updatedPrize);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi cập nhật prize {input.Id}: {ex.Message}", ex);
            return Result<PrizeResponseDto>.Failure("Không thể cập nhật phần thưởng");
        }
    }

    public async Task<Result<List<PrizeResponseDto>>> GetAllPrizesAsync(string campaignId)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return Result<List<PrizeResponseDto>>.Failure("Không tìm thấy cấu hình gamification");
            }
            var query = @"
                query GetAllPrizes($campaignId: String!){
                    getAllPrizes(input: {campaignId: $campaignId}){
                        id
                        name
                        type
                        avatarLink
                        quantity
                        stock
                        externalPrizeId
                    }
                }
            ";
            var variables = new { campaignId = campaignId };

            var responseResult = await ExecuteGraphQLRequestAsync<object, GetAllPrizesResponseWrapperDto>(query, variables, settings.AccessToken);
            if (!responseResult.IsSuccess)
            {
                return Result<List<PrizeResponseDto>>.Failure("Lỗi khi lấy danh sách phần thưởng");
            }

            var prizes = responseResult.Data.GetAllPrizes;

            // Lấy thông tin category từ database bên mình và merge vào response
            if (prizes != null && prizes.Any())
            {
                var localPrizes = await _prizeRepository.GetAllAsync();
                foreach (var prize in prizes)
                {
                    var localPrize = localPrizes.FirstOrDefault(p => p.PrizeGameId == prize.ExternalPrizeId);
                    if (localPrize != null)
                    {
                        prize.Category = localPrize.Category;
                        prize.ExternalPrizeId = localPrize.ExternalPrizeId;
                    }
                }
            }

            return Result<List<PrizeResponseDto>>.Success(prizes ?? new List<PrizeResponseDto>());
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy danh sách prizes cho campaign {campaignId}: {ex.Message}", ex);
            return Result<List<PrizeResponseDto>>.Failure("Không thể lấy danh sách phần thưởng");
        }
    }

    public async Task<Result<GameSessionResponseDto>> StartGameSessionAsync(string shopId, User user)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return Result<GameSessionResponseDto>.Success(new GameSessionResponseDto
                {
                    IsAvailable = false,
                    Status = "ERROR",
                    Message = "Không tìm thấy cấu hình gamification"
                });
            }
            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification settings not found");
                return Result<GameSessionResponseDto>.Success(new GameSessionResponseDto
                {
                    IsAvailable = false,
                    Status = "ERROR",
                    Message = "Không tìm thấy cấu hình gamification cho shop"
                });
            }

            var checksum = "";
            var requestedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
            string thumbnailLink = null;
            DateTime? startTime = null;
            DateTime? endTime = null;

            if (!string.IsNullOrEmpty(shopSetting.CampaignId) && shopSetting.IsActived)
            {
                try
                {
                    var campaignQuery = @"
                        query GetCampaign($id: String!, $zaloAppId: String) {
                            getCampaign(id: $id, zaloAppId: $zaloAppId) {
                                id
                                name
                                thumbnailLink
                                startTime
                                endTime
                                externalCampaignSecret
                            }
                        }
                    ";
                    var campaignVariables = new { id = shopSetting.CampaignId };
                    var campaignResult = await ExecuteGraphQLRequestAsync<object, GetCampaignResponseWrapperDto>(
                        campaignQuery, campaignVariables, settings.AccessToken);

                    if (campaignResult.IsSuccess && campaignResult.Data?.GetCampaign != null)
                    {
                        var startTimeStr = campaignResult.Data.GetCampaign.StartTime;
                        var endTimeStr = campaignResult.Data.GetCampaign.EndTime;
                        if (!string.IsNullOrEmpty(startTimeStr) && !string.IsNullOrEmpty(endTimeStr))
                        {
                            startTime = DateTime.Parse(startTimeStr);
                            endTime = DateTime.Parse(endTimeStr);
                            var now = DateTimes.Now();

                            if (now < startTime)
                            {
                                return Result<GameSessionResponseDto>.Success(new GameSessionResponseDto
                                {
                                    CampaignId = shopSetting.CampaignId,
                                    ApiUrl = _promogameApiUrl,
                                    ThumbnailLink = campaignResult.Data.GetCampaign.ThumbnailLink,
                                    RequestedAt = requestedAt,
                                    IsAvailable = false,
                                    Status = "NOT_STARTED",
                                    Message = "Sự kiện chưa bắt đầu",
                                    StartTime = startTime,
                                    EndTime = endTime
                                });
                            }

                            if (now > endTime)
                            {
                                return Result<GameSessionResponseDto>.Success(new GameSessionResponseDto
                                {
                                    CampaignId = shopSetting.CampaignId,
                                    ApiUrl = _promogameApiUrl,
                                    ThumbnailLink = campaignResult.Data.GetCampaign.ThumbnailLink,
                                    RequestedAt = requestedAt,
                                    IsAvailable = false,
                                    Status = "ENDED",
                                    Message = "Sự kiện đã kết thúc",
                                    StartTime = startTime,
                                    EndTime = endTime
                                });
                            }
                        }
                        var checksumInput = $"{user.UserId}:{campaignResult.Data.GetCampaign.ExternalCampaignSecret}:{requestedAt}";
                        checksum = ConvertToMD5(checksumInput);
                        thumbnailLink = campaignResult.Data.GetCampaign.ThumbnailLink;
                    }
                }
                catch (Exception ex)
                {
                    _log.Warn($"Failed to fetch campaign thumbnail for campaignId {shopSetting.CampaignId}: {ex.Message}");
                }
            }
            else
            {
                return Result<GameSessionResponseDto>.Success(new GameSessionResponseDto
                {
                    CampaignId = null,
                    ApiUrl = _promogameApiUrl,
                    RequestedAt = requestedAt,
                    IsAvailable = false,
                    Status = "NO_CAMPAIGN",
                    Message = "Chưa có sự kiện game"
                });
            }

            return Result<GameSessionResponseDto>.Success(new GameSessionResponseDto
            {
                CampaignId = shopSetting.CampaignId,
                ApiUrl = _promogameApiUrl,
                Checksum = checksum,
                ThumbnailLink = thumbnailLink,
                RequestedAt = requestedAt,
                IsAvailable = true,
                Status = "AVAILABLE",
                Message = "Game session is available",
                StartTime = startTime,
                EndTime = endTime
            });
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi khởi tạo game session cho shop {shopId}: {ex.Message}", ex);
            return Result<GameSessionResponseDto>.Failure("Không thể khởi tạo phiên game");
        }
    }

    private static string ConvertToMD5(string input)
    {
        using (MD5 md5 = MD5.Create())
        {
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashBytes = md5.ComputeHash(inputBytes);
            return Convert.ToHexString(hashBytes).ToLower();
        }
    }
    public async Task<Result<BrandResponseDataDto>> CreateGameBrand(string shopId)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync() ?? await EnsureValidGamificationTokenAsync(shopId);
            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            Shop shop = _shopRepository.FindByShopId(shopId);

            if (shop == null)
                return Result<BrandResponseDataDto>.Failure("Không tìm thấy shop");

            // If shopSetting exists, update the brand
            if (shopSetting != null)
            {
                var updateMutation = @"
                    mutation UpdateBrand($externalBrandId: String!, $brandId: String!, $zaloOaId: String!){
                        updateBrand(input: { id: $brandId, externalBrandId: $externalBrandId, zaloOaId: $zaloOaId}) {
                            id
                            name
                            avatarLink
                            externalBrandId
                            zaloOaId
                        }
                    }
                ";
                var variables = new
                {
                    externalBrandId = shopId,
                    brandId = shopSetting.GameBrandId,
                    zaloOaId = shop.OaId
                };

                var result = await ExecuteGraphQLRequestWithTokenRefreshAsync(settings, updateMutation, variables, async (token) =>
                {
                    var responseResult = await ExecuteGraphQLRequestAsync<object, UpdateBrandResponseWrapperDto>(updateMutation, variables, token);
                    if (!responseResult.IsSuccess)
                    {
                        return Result<BrandResponseDataDto>.Failure("Lỗi khi cập nhật game brand cho shop");
                    }
                    return Result<BrandResponseDataDto>.Success(responseResult.Data.UpdateBrand);
                });
                return result;
            }

            // If shopSetting doesn't exist, create a new brand
            var mutation = @"
                mutation CreateBrand($name: String!, $externalBrandId: String!, $avatar: Upload!, $zaloOaId: String!){
                    createBrand(
                        input:
                        {
                            name: $name,
                            avatar: {file: $avatar}
                            externalBrandId: $externalBrandId
                            zaloOaId: $zaloOaId})
                            {
                                id
                                name
                                avatarLink
                                externalBrandId
                                zaloOaId
                            }
                        }
                ";
            var cleanShopId = shopId.Replace("-", "");
            var shortShopId = cleanShopId.Length > 4 ? cleanShopId.Substring(0, 4) : cleanShopId;
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var randomSuffix = Guid.NewGuid().ToString("N").Substring(0, 4);
            var name = $"shop{shortShopId}{timestamp}{randomSuffix}";
            var operationsVariables = new
            {
                name = name,
                externalBrandId = shopId,
                avatar = (object)null,
                zaloOaId = shop.OaId
            };

            var operationsObject = new
            {
                Query = mutation,
                Variables = operationsVariables
            };

            var requestSerializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
            var mapJson = "{\"0\": [\"variables.avatar\"]}";

            using var multipartContent = new MultipartFormDataContent();
            multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
            multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");

            // Create a fake avatar file (1x1 pixel transparent PNG)
            var fakeAvatarBytes = Convert.FromBase64String("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
            multipartContent.Add(new ByteArrayContent(fakeAvatarBytes), "0", "default-avatar.png");

            var brandResult = await ExecuteMultipartRequestWithTokenRefreshAsync<CreateBrandResponseWrapperDto>(settings, async (token) =>
            {
                var result = await ExecuteMultipartRequestAsync<CreateBrandResponseWrapperDto>(
                    mutation, operationsVariables, token, multipartContent, "CreateBrand");

                if (result.IsSuccess)
                {
                    var gamificationShop = new GamificationShop
                    {
                        ShopId = shopId,
                        GameBrandId = result.Data.CreateBrand.Id
                    };
                    await _gamificationShopRepository.Insert(gamificationShop);
                }

                return result;
            });

            if (!brandResult.IsSuccess)
            {
                return Result<BrandResponseDataDto>.Failure("Lỗi khi tạo game brand cho shop");
            }
            return Result<BrandResponseDataDto>.Success(brandResult.Data.CreateBrand);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi tạo game brand cho shop {shopId}: {ex.Message}", ex);
            return Result<BrandResponseDataDto>.Failure("Không thể tạo game brand");
        }
    }

    private async Task<Result<T>> ExecuteMultipartRequestWithTokenRefreshAsync<T>(Gamification settings, Func<string, Task<Result<T>>> executeWithToken)
    {
        var result = await executeWithToken(settings.AccessToken);
        if (result.IsSuccess)
        {
            return result;
        }

        // Check if this is a token expiration error
        if (result.Message.Contains("Phiên đăng nhập đã hết hạn"))
        {
            _log.Info("Token expired, attempting to refresh and retry...");
            var refreshResult = await RefreshTokenAndRetryAsync();
            if (refreshResult.IsSuccess)
            {
                return await executeWithToken(refreshResult.Data);
            }
            return Result<T>.Failure("Phiên đăng nhập đã hết hạn");
        }

        return result;
    }

    private async Task<Result<TResponseData>> ExecuteMultipartRequestAsync<TResponseData>(
        string mutation,
        object variables,
        string accessToken,
        MultipartFormDataContent multipartContent,
        string operationName)
    {
        using var client = _httpClientFactory.CreateClient("PromogameClient");
        client.DefaultRequestHeaders.UserAgent.ParseAdd("ECommerceBackend/1.0");

        if (!string.IsNullOrEmpty(accessToken))
        {
            client.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
        }
        client.DefaultRequestHeaders.Add("X-Apollo-Operation-Name", operationName);

        try
        {
            var responseMessage = await client.PostAsync(_promogameApiUrl, multipartContent);
            var responseContent = await responseMessage.Content.ReadAsStringAsync();

            if (!responseMessage.IsSuccessStatusCode)
            {
                _log.Error($"Failed GraphQL request. Status: {responseMessage.StatusCode}, URL: {_promogameApiUrl}, Details: {responseContent}");
                return Result<TResponseData>.Failure("Yêu cầu đến dịch vụ gamification thất bại");
            }

            var responseDeserializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var gqlResponse = JsonSerializer.Deserialize<PromogameGraphQLResponse<TResponseData>>(responseContent, responseDeserializerOptions);

            if (gqlResponse?.Errors != null && gqlResponse.Errors.Any())
            {
                var errorMessages = string.Join("; ", gqlResponse.Errors.Select(e => e.Message));
                _log.Error($"GraphQL error: {errorMessages}. Query: {mutation}");

                if (IsTokenExpiredError(errorMessages))
                {
                    return Result<TResponseData>.Failure("Phiên đăng nhập đã hết hạn");
                }

                return Result<TResponseData>.Failure("Lỗi xử lý dữ liệu từ dịch vụ gamification");
            }

            return Result<TResponseData>.Success(gqlResponse.Data);
        }
        catch (HttpRequestException ex)
        {
            _log.Error($"HTTP request to Promogame API failed. URL: {_promogameApiUrl}", ex);
            return Result<TResponseData>.Failure("Không thể kết nối đến dịch vụ gamification");
        }
    }

    public async Task<Result<string>> CreateCampaign(GameCampaignDto gameCampaign)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync() ?? await EnsureValidGamificationTokenAsync(gameCampaign.ShopId);
            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(gameCampaign.ShopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification settings not found");
                return Result<string>.Failure("Không tìm thấy cấu hình gamification cho shop");
            }
            var shop = _shopRepository.FindByShopId(gameCampaign.ShopId);
            if (shop == null)
            {
                _log.Error("Shop not found");
                return Result<string>.Failure("Không tìm thấy thông tin shop");
            }
            string env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            bool isProduction = env.Equals("Production", StringComparison.OrdinalIgnoreCase);
            var cleanShopId = shop.ShopId.Replace("-", "");
            var shortShopId = cleanShopId.Length > 4 ? cleanShopId.Substring(0, 4) : cleanShopId;
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var randomSuffix = Guid.NewGuid().ToString("N").Substring(0, 4);
            var campaign = $"campaign{shortShopId}{timestamp}{randomSuffix}";
            var mutation = @"
                        mutation CreateCampaign($name: String!, $gameId: String!, $brandId: String!, $thumbnail: Upload!,
                        $startTime: DateTime!, $endTime: DateTime!, $externalGiftLink: String!, $ingestEventUrl: String!, $externalCampaignId: String!)
                        {createCampaign(input: { name: $name, thumbnail: { file: $thumbnail },
                        gameId: $gameId, stackScore: true, initialTickets: 1, dailyTickets: 1, authenticatePhase: AUTHENTICATE_PHASE_BEFORE_PLAY,
                        brandId: $brandId, allowEnterTicketCode: false, gamePrizeEnable: true, freeToPlay: false, leaderboardEnable: true,
                        parameters: {}, startTime: $startTime, endTime: $endTime, externalGiftLink: $externalGiftLink,
                        ingestEventUrl: $ingestEventUrl, externalCampaignId: $externalCampaignId }) { id } }
                    ";

            var operationsVariables = new
            {
                name = gameCampaign.Name,
                thumbnail = (object)null,
                gameId = gameCampaign.GameId,
                brandId = shopSetting.GameBrandId,
                startTime = gameCampaign.StartTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                endTime = gameCampaign.EndTime.ToString("yyyy-MM-ddTHH:mm:ss"),
                externalGiftLink = "gift_event",
                ingestEventUrl = _domainWebhook,
                externalCampaignId = campaign
            };

            var operationsObject = new
            {
                Query = mutation,
                Variables = operationsVariables
            };

            var requestSerializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
            var mapJson = "{\"0\": [\"variables.thumbnail\"]}";

            using var multipartContent = new MultipartFormDataContent();
            multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
            multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");

            if (gameCampaign.Thumbnail != null && gameCampaign.Thumbnail.Length > 0)
            {
                multipartContent.Add(new StreamContent(gameCampaign.Thumbnail.OpenReadStream()), "0", gameCampaign.Thumbnail.FileName);
            }

            var campaignResult = await ExecuteMultipartRequestWithTokenRefreshAsync<CreateCampaignResponseWrapperDto>(settings, async (token) =>
            {
                return await ExecuteMultipartRequestAsync<CreateCampaignResponseWrapperDto>(
                    mutation, operationsVariables, token, multipartContent, "CreateCampaign");
            });

            if (!campaignResult.IsSuccess)
            {
                return Result<string>.Failure("Lỗi khi tạo campaign cho shop");
            }

            var campaignId = campaignResult.Data.CreateCampaign.Id;

            return Result<string>.Success(campaignId);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi tạo campaign cho shop {gameCampaign.ShopId}: {ex.Message}", ex);
            return Result<string>.Failure("Không thể tạo chiến dịch game");
        }
    }

    public async Task<Result<string>> UpdateCampaign(UpdateCampaignDto updateCampaign)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return Result<string>.Failure("Không tìm thấy cấu hình gamification");
            }

            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(updateCampaign.ShopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification shop settings not found");
                return Result<string>.Failure("Không tìm thấy cấu hình gamification cho shop");
            }

            // Tạo mutation động dựa trên việc có thumbnail hay không
            var hasThumbnail = updateCampaign.Thumbnail != null && updateCampaign.Thumbnail.Length > 0;

            var mutation = hasThumbnail ?
                @"mutation UpdateCampaign($campaignId: String!, $name: String, $thumbnail: Upload!, $startTime: DateTime, $endTime: DateTime)
                {
                    updateCampaign(input: {
                        id: $campaignId,
                        name: $name,
                        thumbnail: {file: $thumbnail},
                        startTime: $startTime,
                        endTime: $endTime
                    }) {
                        id
                        name
                    }
                }" :
                @"mutation UpdateCampaign($campaignId: String!, $name: String, $startTime: DateTime, $endTime: DateTime)
                {
                    updateCampaign(input: {
                        id: $campaignId,
                        name: $name,
                        startTime: $startTime,
                        endTime: $endTime
                    }) {
                        id
                        name
                    }
                }";

            object operationsVariables;
            if (hasThumbnail)
            {
                operationsVariables = new
                {
                    campaignId = updateCampaign.CampaignId,
                    name = updateCampaign.Name,
                    thumbnail = (object)null,
                    startTime = updateCampaign.StartDate?.ToString("yyyy-MM-ddTHH:mm:ss"),
                    endTime = updateCampaign.EndDate?.ToString("yyyy-MM-ddTHH:mm:ss")
                };
            }
            else
            {
                operationsVariables = new
                {
                    campaignId = updateCampaign.CampaignId,
                    name = updateCampaign.Name,
                    startTime = updateCampaign.StartDate?.ToString("yyyy-MM-ddTHH:mm:ss"),
                    endTime = updateCampaign.EndDate?.ToString("yyyy-MM-ddTHH:mm:ss")
                };
            }

            var operationsObject = new
            {
                Query = mutation,
                Variables = operationsVariables
            };

            var requestSerializerOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                Converters = { new JsonStringEnumConverter(JsonNamingPolicy.CamelCase) }
            };
            var operationsJson = JsonSerializer.Serialize(operationsObject, requestSerializerOptions);
            var mapJson = hasThumbnail ? "{\"0\": [\"variables.thumbnail\"]}" : "{}";

            using var multipartContent = new MultipartFormDataContent();
            multipartContent.Add(new StringContent(operationsJson, Encoding.UTF8, "application/json"), "operations");
            multipartContent.Add(new StringContent(mapJson, Encoding.UTF8, "application/json"), "map");

            if (hasThumbnail)
            {
                multipartContent.Add(new StreamContent(updateCampaign.Thumbnail.OpenReadStream()), "0", updateCampaign.Thumbnail.FileName);
            }

            var campaignResult = await ExecuteMultipartRequestWithTokenRefreshAsync<UpdateCampaignResponseWrapperDto>(settings, async (token) =>
            {
                return await ExecuteMultipartRequestAsync<UpdateCampaignResponseWrapperDto>(
                    mutation, operationsVariables, token, multipartContent, "UpdateCampaign");
            });

            if (!campaignResult.IsSuccess)
            {
                return Result<string>.Failure("Lỗi khi cập nhật campaign cho shop");
            }
            return Result<string>.Success(campaignResult.Data.UpdateCampaign.Id);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi cập nhật campaign cho shop {updateCampaign.ShopId}: {ex.Message}", ex);
            return Result<string>.Failure("Không thể cập nhật chiến dịch game");
        }
    }

    public async Task<Result<CampaignResponseDto>> GetCampaign(string campaignId)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return Result<CampaignResponseDto>.Failure("Không tìm thấy cấu hình gamification");
            }

            // Ensure we have valid tokens by attempting a refresh if needed
            try
            {
                await RefreshTokenAndRetryAsync();
                // Get the updated settings after potential refresh
                settings = await _gamificationRepository.GetSettingsAsync();
            }
            catch (Exception ex)
            {
                _log.Warn($"Failed to refresh token for GetCampaign, using existing tokens: {ex.Message}");
            }

            var result = new CampaignResponseDto
            {
                AccessToken = settings.AccessToken,
                RefreshToken = settings.RefreshToken,
                DashboardUrl = _dashboardUrl,
                CampaignId = campaignId
            };

            return Result<CampaignResponseDto>.Success(result);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy thông tin campaign {campaignId}: {ex.Message}", ex);
            return Result<CampaignResponseDto>.Failure("Không thể lấy thông tin chiến dịch");
        }
    }

    public async Task<Result<List<GameCampaignDto>>> GetListCampaign(string shopId)
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync() ?? await EnsureValidGamificationTokenAsync(shopId);

            var shopSetting = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            if (shopSetting == null)
            {
                _log.Error("Gamification settings not found");
                return Result<List<GameCampaignDto>>.Failure("Không tìm thấy cấu hình gamification cho shop");
            }

            var brandId = shopSetting.GameBrandId;
            var activeCampaignId = shopSetting.CampaignId;
            var isActived = shopSetting.IsActived;

            var query = @"
                            query GetAllCampaigns($brandId: String!, $start: Int!, $count: Int!){
                                getAllCampaigns(filter: {brandId: $brandId}, input: {start: $start, count: $count}){
                                campaigns {
                                    name
                                    id
                                    thumbnailLink
                                    game {
                                        id
                                        name
                                    }
                                    externalCampaignId
                                    startTime
                                    endTime
                                }
                                }
                            }
                        ";

            var variables = new
            {
                brandId = brandId,
                start = 0,
                count = 100
            };

            var response = await ExecuteGraphQLRequestWithTokenRefreshAsync<object, GetAllCampaignsResponseWrapperDto>(
                settings,
                query,
                variables,
                async (token) => await ExecuteGraphQLRequestAsync<object, GetAllCampaignsResponseWrapperDto>(query, variables, token)
            );

            if (!response.IsSuccess)
            {
                return Result<List<GameCampaignDto>>.Failure("Lỗi khi lấy danh sách campaign cho shop");
            }

            if (response.Data?.GetAllCampaigns?.Campaigns == null)
                return Result<List<GameCampaignDto>>.Success(new List<GameCampaignDto>());

            // Gán trường Active - chỉ active khi cả campaignId khớp và IsActived = true
            foreach (var campaign in response.Data.GetAllCampaigns.Campaigns)
            {
                campaign.Active = (campaign.Id == activeCampaignId && isActived);
            }
            return Result<List<GameCampaignDto>>.Success(response.Data.GetAllCampaigns.Campaigns);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy danh sách campaign cho shop {shopId}: {ex.Message}", ex);
            return Result<List<GameCampaignDto>>.Failure("Không thể lấy danh sách chiến dịch game");
        }
    }

    public async Task<Result<GameCampaignDto>> GetCampaignActiveByShopId(string shopId)
    {
        try
        {
            var campaignsResult = await GetListCampaign(shopId);
            if (!campaignsResult.IsSuccess)
            {
                return Result<GameCampaignDto>.Failure("Lỗi khi lấy danh sách campaign cho shop");
            }

            var activeCampaign = campaignsResult.Data?.FirstOrDefault(c => c.Active == true);
            if (activeCampaign == null)
            {
                return Result<GameCampaignDto>.Failure("Không tìm thấy chiến dịch đang hoạt động");
            }

            return Result<GameCampaignDto>.Success(activeCampaign);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy campaign active cho shop {shopId}: {ex.Message}", ex);
            return Result<GameCampaignDto>.Failure("Không thể lấy chiến dịch đang hoạt động");
        }
    }

    public async Task<Result<bool>> ActiveCampaign(string shopId, string campaignId)
    {
        try
        {
            await _gamificationShopRepository.UpdateCampaignIdAsync(shopId, campaignId);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi active campaign {campaignId} cho shop {shopId}: {ex.Message}", ex);
            return Result<bool>.Failure("Không thể kích hoạt chiến dịch");
        }
    }

    public async Task<Result<bool>> Activate(string shopId)
    {
        try
        {
            var brandResult = await CreateGameBrand(shopId);
            if (!brandResult.IsSuccess)
            {
                return Result<bool>.Failure("Không thể tạo game brand");
            }

            // Bật chiến dịch hiện tại bằng cách set IsActived thành true
            await _gamificationShopRepository.UpdateCampaignStatusAsync(shopId, true);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi activate gamification cho shop {shopId}: {ex.Message}", ex);
            return Result<bool>.Failure("Không thể kích hoạt gamification");
        }
    }

    public async Task<Result<bool>> Deactivate(string shopId)
    {
        try
        {
            // Tắt chiến dịch bằng cách set IsActived thành false
            await _gamificationShopRepository.UpdateCampaignStatusAsync(shopId, false);
            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi deactivate gamification cho shop {shopId}: {ex.Message}", ex);
            return Result<bool>.Failure("Không thể tắt gamification");
        }
    }

    public async Task<Result<GamificationShop>> GetGameShopInfo(string shopId)
    {
        try
        {
            var gamificationShop = await _gamificationShopRepository.GetGameBrandIdByShopId(shopId);
            return Result<GamificationShop>.Success(gamificationShop);
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy thông tin game shop {shopId}: {ex.Message}", ex);
            return Result<GamificationShop>.Failure("Không thể lấy thông tin game shop");
        }
    }

    public async Task<Result<List<GameDto>>> GetAllGamesAsync()
    {
        try
        {
            var settings = await _gamificationRepository.GetSettingsAsync();
            if (settings == null)
            {
                _log.Error("Gamification settings not found");
                return Result<List<GameDto>>.Success(new List<GameDto>());
            }
            var query = @"
                    query {
                      getAllGames {
                        id
                        name
                      }
                    }
                ";
            var response = await ExecuteGraphQLRequestWithTokenRefreshAsync<object, GetAllGamesResponseWrapperDto>(
                settings,
                query,
                null,
                async (token) => await ExecuteGraphQLRequestAsync<object, GetAllGamesResponseWrapperDto>(query, null, token)
            );
            if (!response.IsSuccess)
            {
                return Result<List<GameDto>>.Failure("Lỗi khi lấy danh sách games");
            }
            return Result<List<GameDto>>.Success(response.Data?.GetAllGames ?? new List<GameDto>());
        }
        catch (Exception ex)
        {
            _log.Error($"Lỗi khi lấy danh sách games: {ex.Message}", ex);
            return Result<List<GameDto>>.Failure("Không thể lấy danh sách games");
        }
    }
}