using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;

using AutoMapper;

using log4net;

using MongoDB.Bson;

using static App.ECommerce.Resource.Enums.AffiliationEnum;

namespace App.ECommerce.ProcessFlow.Implement;

public class AffiliateFlow : IAffiliateFlow
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(AffiliateFlow));
    private readonly ICommissionsReportRepository _commissionsReportRepository;
    private readonly ICommissionsConfigRepository _commissionsConfigRepository;
    private readonly ICommissionsReportFlow _commissionReportFlow;
    private readonly IShopRepository _shopRepository;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;

    public AffiliateFlow(
        ICommissionsReportFlow commissionReportFlow,
        ICommissionsReportRepository commissionsReportRepository,
        ICommissionsConfigRepository commissionsConfigRepository,
        IShopRepository shopRepository,
        IUserRepository userRepository,
        IMapper mapper)
    {
        _commissionsReportRepository = commissionsReportRepository;
        _commissionsConfigRepository = commissionsConfigRepository;
        _commissionReportFlow = commissionReportFlow;
        _shopRepository = shopRepository;
        _userRepository = userRepository;
        _mapper = mapper;
    }

    public async Task ProcessCommissionPayments()
    {
        var today = System.DateTime.UtcNow.Date;
        var day = today.Day;     // Lấy ngày
        var month = (short)today.Month; // Lấy tháng
        var year = (short)today.Year;   // Lấy năm

        // Lấy danh sách tất cả shop có Status = Active
        var shops = await _shopRepository.GetActivedShops();
        var shopCommissions = await _commissionsConfigRepository.FindCommissionsConfigs();

        // Kiểm tra từng shop
        foreach (var shop in shops)
        {
            var shopId = shop.ShopId;
            var commissionConfig = shopCommissions.FirstOrDefault(c => c.ShopId == shopId);

            if (commissionConfig == null)
            {
                _log.Info($"No commission config found for ShopId: {shopId}");
                continue;
            }

            int paymentDue = commissionConfig.AdvancedCommissionsConfig?.PaymentDue ?? 1; // Mặc định là 15 nếu không tìm thấy

            // Kiểm tra xem hôm nay có phải là PaymentDue của shop không
            if (day.Equals(paymentDue))
            {
                try
                {
                    // Gọi hàm ApprovePaymentForCommission cho shop này
                    var previousMonthDate = today.AddMonths(-1); // Lấy ngày tháng trước
                    await ApprovePaymentForCommission(shopId, previousMonthDate.Month, previousMonthDate.Year);
                    _log.Info($"ApprovePaymentForCommission executed for ShopId: {shopId} at {System.DateTime.UtcNow}");
                }
                catch (Exception ex)
                {
                    _log.Info($"Error executing ApprovePaymentForCommission for ShopId: {shopId}");
                }
            }
        }
    }

    private async Task ApprovePaymentForCommission(string shopId, int month, int year)
    {
        var inputDto = new GetPartnerCommissionsByMonthYearInputDto
        {
            ShopId = shopId,
            Month = month,
            Year = year,
            Paging = new Paging
            {
                PageSize = int.MaxValue, // Lấy tất cả
                PageIndex = 0,           // Trang đầu
                Search = ""              // Search rỗng
                                         // Bỏ NameType và SortType vì đã có giá trị mặc định trong class Paging
            }
        };

        // Nếu đã thanh toán rồi thì log và không thực hiện thanh toán nữa
        var paidCommissionReportCount = await _commissionReportFlow.GetPaidCommissionReport(inputDto);
        if (paidCommissionReportCount.Total > 0)
        {
            _log.Info($" ShopId: {shopId} commission for month {month} has been paid ");
            return;
        }

        // lấy danh sách đối tác + hoa hồng theo tháng và năm
        var commissionReport = (await _commissionReportFlow.GetPartnerCommissionsByMonthAndYear(inputDto)).Result;

        var insertCommissionReportList = _mapper.Map<List<CommissionsReport>>(commissionReport)
                .Select(commission =>
                {
                    commission.ShopId = shopId;
                    commission.Month = month;
                    commission.Year = year;
                    commission.PaymentStatus = PaymentStatus.Paid;
                    return commission;
                }).ToList();

        // Insert commission reports
        await _commissionsReportRepository.InsertCommissionReports(insertCommissionReportList);
    }


    public async Task DeactivateExpiredAffiliationUsers()
    {
        try
        {
            // Lấy danh sách tất cả shop có Status = Active
            var shops = await _shopRepository.GetActivedShops();
            var shopCommissions = await _commissionsConfigRepository.FindCommissionsConfigs();

            // Kiểm tra từng shop
            foreach (var shop in shops)
            {
                var shopId = shop.ShopId;
                var commissionConfig = shopCommissions.FirstOrDefault(c => c.ShopId == shopId);

                if (commissionConfig == null)
                    continue;

                var expiredDateSinceBecomeAffiliate = commissionConfig.AdvancedCommissionsConfig?.PartnerCommExpiry;

                if (expiredDateSinceBecomeAffiliate == null)
                    continue;

                var expiredUsers = _userRepository.GetListUserExpireAffiliate(shopId, expiredDateSinceBecomeAffiliate.Value);

                foreach (var user in expiredUsers)
                {
                    user.AffiliationStatus = AffiliationTypeStatus.Expired;
                    _userRepository.UpdateUser(user);
                }
            }
        }
        catch (Exception ex)
        {
            _log.Error($"DeactivateExpiredAffiliationUsers Error: {ex.Message}", ex);
            throw;
        }
    }
}