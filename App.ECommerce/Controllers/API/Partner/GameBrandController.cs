using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class GameBrandController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly IGamificationFlow _gamificationFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(GameBrandController));

    public GameBrandController(IStringLocalizer localizer, IGamificationFlow gamificationFlow) : base(localizer)
    {
        _localizer = localizer;
        _gamificationFlow = gamificationFlow;
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] string shopId)
    {
        try
        {

            var result = await _gamificationFlow.CreateGameBrand(shopId);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(result.Data);
        }
        catch (Exception ex)
        {
            _log.Error($"Error in CreateGameBrand for shopId {shopId}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}