using System.Threading.Tasks;
using App.Base.Middleware;
using App.Base.Utilities;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Services.UploadStore;
using App.ECommerce.Setting;
using App.ECommerce.Units;
using App.ECommerce.Units.Attribute;
using App.ECommerce.Units.Enums;
using log4net;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Action = App.ECommerce.Resource.Model.Action;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class PaymentPartnerController : BaseController
{
    private readonly ILog _log4net = log4net.LogManager.GetLogger(typeof(PaymentPartnerController));
    private readonly IServiceScopeFactory _serviceScopeFactory;

    private readonly IPartnerRepository _partnerRepository;
    private readonly IShopRepository _shopRepository;
    private readonly IPaymentRepository _paymentRepository;

    public PaymentPartnerController(IStringLocalizer localizer, IServiceScopeFactory serviceScopeFactory,
        IPartnerRepository partnerRepository,
        IShopRepository shopRepository,
        IPaymentRepository paymentRepository)
        : base(localizer)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _partnerRepository = partnerRepository;
        _shopRepository = shopRepository;
        _paymentRepository = paymentRepository;
    }

    #region Payment

    /// <summary>
    /// Get list Payment for shop (Danh sách phương thức thanh toán của cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <param name="skip"></param>
    /// <param name="limit"></param>
    /// <returns>Result ListPayment for shop</returns>
    // POST: api/partner/PaymentPartner/ListPayment
    [HttpGet]
    public IActionResult ListPayment([FromQuery] RequiredShopDto model, [FromQuery] int skip = 0, [FromQuery] int limit = 99)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            Paging paging = new Paging()
            {
                Search = $"{model.Search}",
                PageIndex = skip / (limit == 0 ? 1 : limit),
                PageSize = limit,
                NameType = TypeSortName.Created,
                SortType = TypeSort.desc
            };
            PagingResult<Payment> listPayment = _paymentRepository.ListItems(paging, model.ShopId);
            List<PaymentDto> listPaymentDto = _mapper.Map<List<PaymentDto>>(listPayment.Result);

            return ResponseData(new { data = listPaymentDto, skip, limit, total = listPayment.Total });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/ListPayment",
                Message = $"Error Partner get list Payment",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/PaymentPartner/ListPayment", ex);
        }
    }

    /// <summary>
    /// Create Payment (Tạo mới phương thức thanh toán cho cửa hàng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result CreatePayment for shop</returns>
    // PUT: api/partner/PaymentPartner/CreatePayment
    [HttpPost]
    public async Task<IActionResult> CreatePayment([FromBody] PaymentDto model)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Shop? shop = _shopRepository.FindByShopId(model.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            Payment payment = _mapper.Map<Payment>(model);
            // Payment payment = new Payment();

            payment = _paymentRepository.CreateItems(payment);

            PaymentDto paymentDto = _mapper.Map<PaymentDto>(payment);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/CreatePayment",
                Message = $"Partner create Payment",
                Exception = null,
                DataObject = null
            });

            return ResponseData(paymentDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Create,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/CreatePayment",
                Message = $"Error Partner create Payment",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/PaymentPartner/CreatePayment", ex, model);
        }
    }

    public class UpdatePhotoPaymentModel
    {

        [FromForm(Name = "PaymentId")]
        public string PaymentId { get; set; }

        [FromForm(Name = "FileUpload")]
        [FileType(10 * 1024 * 1024, new string[] { ".png", ".jpg", ".jpeg", ".jfif" })]
        public IFormFile FileUpload { get; set; }
    }

    /// <summary>
    /// Update photo for payment (Cập nhật ảnh cho phương thức thanh toán)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result upload photo for shop</returns>
    // POST: api/partner/PaymentPartner/UpdatePhotoPayment
    [HttpPost("UpdatePhotoPayment"), DisableRequestSizeLimit]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> UpdatePhotoPayment([FromForm] UpdatePhotoPaymentModel model)
    {
        try
        {

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Payment? payment = _paymentRepository.FindByItemsId(model.PaymentId);
            if (payment == null) return ResponseBadRequest(new CustomBadRequest(localizer("PAYMENT_NOT_FOUND"), this.ControllerContext));


            Shop? shop = _shopRepository.FindByShopId(payment.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            if (shop.PartnerId != partner.PartnerId) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_YOURS"), this.ControllerContext));

            // Validate file size
            if (model.FileUpload == null) return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_NOT_FOUND"), this.ControllerContext));
            if (model.FileUpload.Length == 0) return ResponseBadRequest(new CustomBadRequest(localizer("FILE_UPLOAD_NOT_FOUND"), this.ControllerContext));


            // remove old file
            await S3Upload.DeleteImageS3(new List<string>() { payment.PaymentPhoto });

            // upload new file
            var keyFile = S3Upload.SendMyFileToS3(model.FileUpload, model.FileUpload.FileName.FixFileName(), "paymentPhotos").Result;
            if (!string.IsNullOrEmpty(keyFile)) payment.PaymentPhoto = $"{keyFile}";
            payment = _paymentRepository.UpdateItems(payment);
            PaymentDto paymentDto = _mapper.Map<PaymentDto>(payment);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/UpdatePhotoPayment",
                Message = $"Partner Update PhotoPayment",
                Exception = null,
                DataObject = null
            });

            return ResponseData(paymentDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/UpdatePhotoPayment",
                Message = $"Error Partner Update PhotoPayment",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/ShopPartner/UpdatePhotoPayment", ex);
        }
    }

    /// <summary>
    /// Update Payment (Cập nhật phương thức thanh toán cho cửa hảng)
    /// </summary>
    /// <param name="model"></param>
    /// <returns>The result UpdatePayment for shop</returns>
    // POST: api/partner/PaymentPartner/UpdatePayment
    [HttpPut]
    public async Task<IActionResult> UpdatePayment([FromBody] PaymentDto model)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            // Validate
            Payment? payment = _paymentRepository.FindByItemsId(model.PaymentId);
            if (payment == null) return ResponseBadRequest(new CustomBadRequest(localizer("PAYMENT_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(payment.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));

            if (!string.IsNullOrEmpty(model.Name))
            {
                payment.Name = model.Name;
            }
            if (!string.IsNullOrEmpty(model.Detail))
            {
                payment.Detail = model.Detail;
            }
            if (model.Position != null)
            {
                payment.Position = model.Position;
            }
            payment.Platform = model.Platform;

            if (model.TypePay.HasValue)
            {
                payment.TypePay = model.TypePay;
            }

            if (!string.IsNullOrEmpty(model.BankAccountNumber))
            {
                payment.BankAccountNumber = model.BankAccountNumber;
            }
            if (!string.IsNullOrEmpty(model.BankShortCode))
            {
                payment.BankShortCode = model.BankShortCode;
            }
            if (!string.IsNullOrEmpty(model.CustomerBankName))
            {
                payment.CustomerBankName = model.CustomerBankName;
            }
            if (!string.IsNullOrEmpty(model.IdentificationNumber))
            {
                payment.IdentificationNumber = model.IdentificationNumber;
            }
            if (!string.IsNullOrEmpty(model.PhoneNumber))
            {
                payment.PhoneNumber = model.PhoneNumber;
            }
            if (!string.IsNullOrEmpty(model.Alias))
            {
                payment.Alias = model.Alias;
            }
            if (!string.IsNullOrEmpty(model.BankAccountId))
            {
                payment.BankAccountId = model.BankAccountId;
            }
            if (!string.IsNullOrEmpty(model.BankName))
            {
                payment.BankName = model.BankName;
            }
            if (!string.IsNullOrEmpty(model.BankNumber))
            {
                payment.BankNumber = model.BankNumber;
            }
            if (!string.IsNullOrEmpty(model.BankUserName))
            {
                payment.BankUserName = model.BankUserName;
            }

            payment.IsActive = model.IsActive;

            payment.Updated = DateTimes.Now();
            payment = _paymentRepository.UpdateItems(payment);

            PaymentDto paymentDto = _mapper.Map<PaymentDto>(payment);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/UpdatePayment",
                Message = $"Partner update Payment",
                Exception = null,
                DataObject = payment
            });

            return ResponseData(paymentDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Update,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/UpdatePayment",
                Message = $"Error Partner update Payment",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/PaymentPartner/UpdatePayment", ex, model);
        }
    }

    /// <summary>
    /// Delete Payment (Xóa phương thức thanh toán cho cửa hàng)
    /// </summary>
    /// <param name="paymentId"></param>
    /// <returns>The result DeletePayment for shop</returns>
    // DELETE: api/partner/PaymentPartner/{paymentId}
    [HttpDelete("{paymentId}")]
    [MultiPolicysAuthorizeAttribute(Policys = RolePrefix.Partner, Rules = "")]
    public async Task<IActionResult> DeletePayment(string paymentId)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Payment payment = _paymentRepository.FindByItemsId(paymentId);
            if (payment == null) return ResponseBadRequest(new CustomBadRequest(localizer("PAYMENT_NOT_FOUND"), this.ControllerContext));

            Shop? shop = _shopRepository.FindByShopId(payment.ShopId);
            if (shop == null) return ResponseBadRequest(new CustomBadRequest(localizer("SHOP_NOT_FOUND"), this.ControllerContext));
            await S3Upload.DeleteImageS3(new List<string>() { payment.PaymentPhoto });
            payment = _paymentRepository.DeleteItems(payment.PaymentId);

            LogEvent(new EventLogDto
            {
                RefId = partnerId,
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Success,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/DeletePayment",
                Message = $"Partner delete Payment",
                Exception = null,
                DataObject = payment
            });

            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true, Message = localizer("PAYMENT_DELETE_SUCCESS") });
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Delete,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/DeletePayment",
                Message = $"Error Partner delete Payment",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/PaymentPartner/DeletePayment", ex);
        }
    }

    /// <summary>
    /// Get detail Payment (Lấy thông tin phương thức thanh toán cho cửa hảng)
    /// </summary>
    /// <param name="paymentId"></param>
    /// <returns>The result DetailPayment for shop</returns>
    // GET: api/partner/PaymentPartner/DetailPayment
    [HttpGet("{paymentId}")]
    public async Task<IActionResult> DetailPayment(string paymentId)
    {
        try
        {
            //if (!CheckIsPlatform(new SysPlatform[] { SysPlatform.ROLE_WEB })) return ResponseBadRequest(new CustomBadRequest(localizer("BASE_NOT_SUPPORT_PLATFORM"), this.ControllerContext));

            string partnerId = GetUserIdAuth();
            Partner partner = await _partnerRepository.FindByPartnerId(partnerId);
            if (partner == null) return ResponseUnauthorized(new CustomBadRequest(localizer("BASE_USER_AUTH_NOT_FOUND"), this.ControllerContext));

            Payment payment = _paymentRepository.FindByItemsId(paymentId);
            if (payment == null) return ResponseBadRequest(new CustomBadRequest(localizer("PAYMENT_NOT_FOUND"), this.ControllerContext));

            PaymentDto paymentDto = _mapper.Map<PaymentDto>(payment);

            //LogUserEvent(_log4net, Action.Load, Status.Success, $"{RoutePrefix.PARTNER}/PaymentPartner/DetailPayment", $"Partner get detail Payment", null, null);
            return ResponseData(paymentDto);
        }
        catch (Exception ex)
        {
            LogEvent(new EventLogDto
            {
                RefId = "",
                RefType = TypeFor.Partner,
                Action = LogActionEnum.Load,
                Status = LogStatusEnum.Error,
                ActionAPI = $"{RoutePrefix.PARTNER}/PaymentPartner/DetailPayment",
                Message = $"Error Partner get detail Payment",
                Exception = ex,
                DataObject = null
            });

            return LogExceptionEvent(_log4net, $"{RoutePrefix.PARTNER}/PaymentPartner/DetailPayment", ex);
        }
    }

    #endregion
}