using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class GameController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly IGamificationFlow _gamificationFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(GameController));

    public GameController(IStringLocalizer localizer, IGamificationFlow gamificationFlow) : base(localizer)
    {
        _localizer = localizer;
        _gamificationFlow = gamificationFlow;
    }

    [HttpGet]
    public async Task<IActionResult> GetAllGames()
    {
        try
        {
            var result = await _gamificationFlow.GetAllGamesAsync();
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = result.Data });
        }
        catch (System.Exception ex)
        {
            _log.Error($"Error in GetAllGames: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}