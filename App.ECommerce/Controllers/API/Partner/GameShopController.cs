using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using System.ComponentModel.DataAnnotations;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class GameShopController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly IGamificationFlow _gamificationFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(GameShopController));

    public GameShopController(IStringLocalizer localizer, IGamificationFlow gamificationFlow) : base(localizer)
    {
        _localizer = localizer;
        _gamificationFlow = gamificationFlow;
    }

    [HttpGet]
    public async Task<IActionResult> GetGameShopInfo([FromQuery][Required] string shopId)
    {
        try
        {
            var result = await _gamificationFlow.GetGameShopInfo(shopId);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = result.Data });
        }
        catch (System.Exception ex)
        {
            _log.Error($"Error in GetGameShopInfo for shopId {shopId}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPut("activate")]
    public async Task<IActionResult> Activate([FromQuery][Required] string shopId)
    {
        try
        {
            var result = await _gamificationFlow.Activate(shopId);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in Activate for shopId {shopId}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPut("deactivate")]
    public async Task<IActionResult> Deactivate([FromQuery][Required] string shopId)
    {
        try
        {
            var result = await _gamificationFlow.Deactivate(shopId);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = true });
        }
        catch (Exception ex)
        {
            _log.Error($"Error in Deactivate for shopId {shopId}: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}