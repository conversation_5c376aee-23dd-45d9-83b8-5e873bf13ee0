using App.Base.Middleware;
using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Resource.Dtos.GamificationDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Setting;
using App.ECommerce.Units;

using log4net;

using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace App.ECommerce.Controllers.API;

[ApiController]
[Produces("application/json")]
[Route(RoutePrefix.API_PARTNER)]
[ApiExplorerSettings(GroupName = "partner-v1")]
[Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
[MultiPolicysAuthorize(Policys = RolePrefix.Partner, Rules = "")]
public class GamePrizeController : BaseController
{
    private readonly IStringLocalizer _localizer;
    private readonly IGamificationFlow _gamificationFlow;
    private readonly ILog _log = LogManager.GetLogger(typeof(GamePrizeController));

    public GamePrizeController(IStringLocalizer localizer, IGamificationFlow gamificationFlow) : base(localizer)
    {
        _localizer = localizer;
        _gamificationFlow = gamificationFlow;
    }

    [HttpGet]
    public async Task<IActionResult> GetAll(string campaignId)
    {
        try
        {
            var result = await _gamificationFlow.GetAllPrizesAsync(campaignId);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(result);
        }
        catch (Exception ex)
        {
            _log.Error($"Error in GetAllPrizes: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromForm] CreatePrizeInputDto input)
    {
        try
        {
            var result = await _gamificationFlow.CreatePrizeAsync(input);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(result);
        }
        catch (Exception ex)
        {
            _log.Error($"Error in CreatePrize: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }

    [HttpPut]
    public async Task<IActionResult> Update([FromForm] UpdatePrizeInputDto input)
    {
        try
        {
            var result = await _gamificationFlow.UpdatePrizeAsync(input);
            if (!result.IsSuccess)
                return ResponseBadRequest(new CustomBadRequest(string.Join(", ", result.Errors.Select(e => localizer(e))), this.ControllerContext));
            return ResponseData(result.Data);
        }
        catch (Exception ex)
        {
            _log.Error($"Error in UpdatePrize: {ex.Message}", ex);
            return ResponseData(new { Timestamp = DateTimes.Now(), Result = false, Message = ex.Message });
        }
    }
}